import React, { useState } from "react"
import { ArrowRight, CheckSquare, Code, Folder, BookOpen, GraduationCap, Rocket, ShoppingCart, Sparkles, Bot } from "lucide-react"
import { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiJavascript, SiGit } from "react-icons/si"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useNavigate } from "react-router-dom"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

import DeviceMockup from "@/components/DeviceMockup"
import ChatAssistant from "@/components/ChatAssistant"
import Section from "@/components/Section"
import InteractiveTerminal from "@/components/InteractiveTerminal"

export default function Dashboard() {
  const navigate = useNavigate()
  const [isChatO<PERSON>, setIsChatOpen] = useState(false)

  const currentDate = new Date()
  const graduationDate = new Date('2024-05-30')
  const monthsCoding = Math.floor((currentDate.getTime() - graduationDate.getTime()) / (1000 * 3600 * 24 * 30))

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  const staggerContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  }

  const featuredProjects = [
    {
      id: 1,
      title: "E-commerce Platform",
      description: "A full-stack retail app built with React and Next.js",
      image: "/api/placeholder/600/400",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"],
      status: "Completed",
      highlight: "Full-stack development with modern architecture"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "Collaborative workspace with real-time updates",
      image: "/api/placeholder/600/400",
      technologies: ["React", "Prisma", "PostgreSQL", "Tailwind CSS"],
      status: "In Progress",
      highlight: "Real-time collaboration features"
    },
    {
      id: 3,
      title: "Portfolio Website",
      description: "Modern portfolio showcasing projects and skills",
      image: "/api/placeholder/600/400",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      status: "Completed",
      highlight: "Clean design with smooth interactions"
    }
  ]

  const coreSkills = [
    { name: "React", category: "Frontend", icon: SiReact },
    { name: "TypeScript", category: "Frontend", icon: SiTypescript },
    { name: "Next.js", category: "Frontend", icon: SiNextdotjs },
    { name: "Tailwind CSS", category: "Styling", icon: SiTailwindcss },
    { name: "JavaScript", category: "Frontend", icon: SiJavascript },
    { name: "Git", category: "Tools", icon: SiGit }
  ]

  const journeyMilestones = [
    {
      date: "May 2024",
      title: "Computer Engineering Graduate",
      description: "Graduated with a degree in Computer Engineering",
      icon: GraduationCap,
      type: "education"
    },
    {
      date: "Jun 2024",
      title: "Frontend Development Journey Begins",
      description: "Started focusing on modern web development",
      icon: Rocket,
      type: "career"
    },
    {
      date: "Aug 2024",
      title: "First React Project",
      description: "Built my first React application",
      icon: SiReact,
      type: "project"
    },
    {
      date: "Oct 2024",
      title: "TypeScript Mastery",
      description: "Adopted TypeScript for better code quality",
      icon: SiTypescript,
      type: "skill"
    },
    {
      date: "Jan 2025",
      title: "Portfolio Launch",
      description: "Launched this modern portfolio website",
      icon: Sparkles,
      type: "project"
    },
    {
      date: "Present Day",
      title: "Seeking New Opportunities",
      description: "Open to exciting frontend development roles",
      icon: Sparkles,
      type: "career",
      isPresent: true
    }
  ]

  // Helper function to render icons safely
  const renderIcon = (IconComponent: any, className: string) => {
    if (typeof IconComponent === 'function') {
      return React.createElement(IconComponent, { className })
    }
    return null
  }

  const currentlyLearning = [
    { name: "Advanced React Patterns", icon: Code },
    { name: "TypeScript Best Practices", icon: BookOpen },
    { name: "Frontend Performance", icon: Rocket },
    { name: "UI/UX Design Principles", icon: Sparkles }
  ]

  const workingOn = [
    { name: "Personal Portfolio Website", status: "Active", icon: Folder },
    { name: "E-commerce Platform", status: "Planning", icon: ShoppingCart },
    { name: "Task Management App", status: "Planning", icon: CheckSquare }
  ]

  return (
    <>
      <div className="relative container mx-auto px-6 md:px-8 py-12 md:py-16 max-w-6xl z-10">
      
        {/* Hero Section */}
        <motion.div
          className="mb-24 md:mb-32"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8 order-1 lg:order-1">
              <div className="space-y-6">
                <motion.div
                  className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  Available for new opportunities
                </motion.div>

                <motion.h1
                  className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-800 dark:text-slate-100 leading-tight tracking-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  I craft digital experiences that{' '}
                  <motion.span
                    className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                  >
                    users love
                  </motion.span>
                </motion.h1>

                <motion.p
                  className="text-lg md:text-xl text-slate-600 dark:text-slate-300 leading-relaxed"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  Fresh Computer Engineering graduate turned frontend developer.
                  I build modern, responsive web applications with clean code and exceptional user experiences.
                </motion.p>

                <motion.div
                  className="bg-slate-900/10 dark:bg-slate-100/10 rounded-lg p-4 border border-slate-200/50 dark:border-slate-700/50"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                >
                  <p className="text-sm text-slate-600 dark:text-slate-400 font-mono">
                    <span className="text-primary">→</span> Try the interactive terminal on the right!
                  </p>
                  <p className="text-xs text-slate-500 dark:text-slate-500 font-mono mt-1">
                    Type <code className="bg-slate-200 dark:bg-slate-800 px-1 rounded">help</code> to get started
                  </p>
                </motion.div>
              </div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 pt-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.1 }}
              >
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate('/projects')}
                    size="lg"
                    className="group relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out px-8 py-4 text-base font-semibold border-0 rounded-xl overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                    <span className="relative z-10">View My Work</span>
                    <ArrowRight className="relative z-10 ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 ease-in-out" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate('/about')}
                    variant="outline"
                    size="lg"
                    className="group relative bg-white/90 hover:bg-white/95 border-2 border-gray-200/80 hover:border-blue-300/60 text-gray-700 hover:text-blue-700 px-8 py-4 text-base font-semibold transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-lg backdrop-blur-sm overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/50 to-blue-50/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                    <span className="relative z-10">About My Journey</span>
                    <ArrowRight className="relative z-10 ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out opacity-0 group-hover:opacity-100" />
                  </Button>
                </motion.div>
              </motion.div>
            </div>

            {/* Right Column - Interactive Terminal */}
            <div className="order-2 lg:order-2">
              <InteractiveTerminal />
            </div>
          </div>
        </motion.div>

        {/* Featured Projects */}
        <Section
          title="Featured Work"
          subtitle="A selection of my best projects showcasing modern web development practices and user-centered design."
        >
          <div className="relative">
            <motion.div
              className="flex gap-6 overflow-x-auto pb-4 scrollbar-hidden cursor-grab active:cursor-grabbing"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerContainerVariants}
              drag="x"
              dragConstraints={{ left: -800, right: 0 }}
              dragElastic={0.1}
              whileDrag={{ cursor: "grabbing" }}
            >
              {featuredProjects.map((project) => (
                <motion.div
                  key={project.id}
                  variants={itemVariants}
                  whileHover={{ y: -2, scale: 1.005 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <Card className="group min-w-[350px] md:min-w-[400px] hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm hover:border-primary/20">
                  <div className="aspect-video bg-gradient-to-br from-primary/5 to-primary/10 rounded-t-lg overflow-hidden relative flex items-center justify-center p-6">
                    <DeviceMockup
                      type={project.id === 1 ? 'desktop' : project.id === 2 ? 'mobile' : 'tablet'}
                      className="max-h-full group-hover:scale-[1.02] transition-transform duration-300"
                    >
                      {project.id === 1 && (
                        <div className="p-4 space-y-3">
                          <div className="h-8 bg-gray-200 rounded"></div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="h-16 bg-blue-100 rounded"></div>
                            <div className="h-16 bg-green-100 rounded"></div>
                            <div className="h-16 bg-purple-100 rounded"></div>
                          </div>
                          <div className="h-4 bg-gray-100 rounded w-3/4"></div>
                        </div>
                      )}
                      {project.id === 2 && (
                        <div className="p-3 space-y-2">
                          <div className="h-6 bg-gray-200 rounded"></div>
                          <div className="space-y-1">
                            <div className="h-8 bg-blue-100 rounded flex items-center px-2">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                              <div className="h-2 bg-blue-300 rounded flex-1"></div>
                            </div>
                            <div className="h-8 bg-green-100 rounded flex items-center px-2">
                              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                              <div className="h-2 bg-green-300 rounded flex-1"></div>
                            </div>
                          </div>
                        </div>
                      )}
                      {project.id === 3 && (
                        <div className="p-4 space-y-3">
                          <div className="h-6 bg-gradient-to-r from-blue-400 to-blue-600 rounded text-white flex items-center justify-center text-xs">
                            Portfolio
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="h-12 bg-primary/20 rounded flex items-center justify-center">
                              <div className="w-6 h-6 bg-primary/60 rounded-full"></div>
                            </div>
                            <div className="space-y-1">
                              <div className="h-2 bg-gray-200 rounded"></div>
                              <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </DeviceMockup>
                    <Badge
                      className={cn(
                        "absolute top-4 right-4 font-medium",
                        project.status === "Completed" || project.status === "Active"
                          ? "bg-green-500/10 text-green-600 border-green-500/20"
                          : project.status === "In Progress" || project.status === "Planning"
                          ? "bg-blue-500/10 text-blue-600 border-blue-500/20"
                          : "bg-gray-500/10 text-gray-600 border-gray-500/20"
                      )}
                    >
                      {project.status}
                    </Badge>
                  </div>

                  <CardHeader className="pb-6 space-y-4">
                    <CardTitle className="group-hover:text-primary transition-colors duration-300 text-xl font-bold">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-300 leading-relaxed">
                      {project.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <div className="flex flex-wrap gap-3">
                      {project.technologies.slice(0, 3).map((tech) => (
                        <Badge key={tech} variant="outline" className="text-xs font-medium px-3 py-1">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge variant="outline" className="text-xs font-medium px-3 py-1">
                          +{project.technologies.length - 3} more
                        </Badge>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <Button
                        onClick={() => navigate('/projects')}
                        variant="ghost"
                        className="flex-1 group/btn bg-slate-50 hover:bg-blue-50 hover:text-blue-700 text-slate-600 transition-all duration-300 ease-in-out border border-slate-200 hover:border-blue-200 rounded-lg hover:shadow-sm"
                      >
                        Live Demo
                        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-300 ease-in-out" />
                      </Button>
                      <Button
                        onClick={() => navigate('/projects')}
                        variant="outline"
                        className="flex-1 group/btn bg-white hover:bg-slate-50 hover:text-slate-700 text-slate-600 transition-all duration-300 ease-in-out border border-slate-200 hover:border-slate-300 rounded-lg hover:shadow-sm"
                      >
                        View Code
                        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-300 ease-in-out" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>

          <div className="text-center mt-12">
            <Button
              onClick={() => navigate('/projects')}
              variant="outline"
              className="group bg-white/80 hover:bg-white/95 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-soft-lg backdrop-blur-sm transform hover:scale-[1.02]"
            >
              See all projects
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out" />
            </Button>
          </div>
        </Section>

        {/* Skills Snapshot */}
        <Section 
          title="Core Technologies"
          subtitle="The tools and technologies I use to bring ideas to life."
        >
          <Card className="border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300">
            <CardContent className="p-8">
              <motion.div
                className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={staggerContainerVariants}
              >
                {coreSkills.map((skill) => (
                  <motion.div
                    key={skill.name}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                    className="group flex flex-col items-center space-y-3 p-4 rounded-xl hover:bg-primary/5 transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2"
                    role="button"
                    tabIndex={0}
                    aria-label={`${skill.name} - ${skill.category} technology`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault()
                        console.log(`Skill selected: ${skill.name}`)
                      }
                    }}
                  >
                    <motion.div
                      className="text-3xl"
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      transition={{ duration: 0.2 }}
                    >
                      {renderIcon(skill.icon, "w-8 h-8")}
                    </motion.div>
                    <div className="text-center">
                      <p className="font-semibold text-sm group-hover:text-primary transition-colors duration-300">
                        {skill.name}
                      </p>
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {skill.category}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>

              <div className="text-center mt-8 pt-6 border-t border-border/50">
                <Button
                  onClick={() => navigate('/skills')}
                  variant="outline"
                  className="group bg-white/80 hover:bg-white/95 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 transition-all duration-300 rounded-xl shadow-sm hover:shadow-soft-lg backdrop-blur-sm transform hover:scale-[1.02]"
                >
                  Explore My Full Skillset
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </Section>

        {/* Current Focus */}
        <Section 
          title="Current Focus"
          subtitle="What I'm building and learning right now to stay at the forefront of web development."
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* What I'm Building */}
            <Card className="group hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm transform hover:scale-[1.005]">
              <CardHeader className="pb-6">
                <CardTitle className="flex items-center gap-3 text-xl font-bold">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                    <Folder className="w-5 h-5 text-primary" />
                  </div>
                  What I'm Building
                </CardTitle>
                <CardDescription className="text-muted-foreground/80">
                  Current projects and ongoing development
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {workingOn.map((project) => (
                    <div key={project.name} className="flex items-center justify-between p-4 rounded-xl hover:bg-muted/30 transition-colors duration-200 group/item border border-border/30">
                      <div className="flex items-center gap-3">
                        {renderIcon(project.icon, cn(
                          "w-4 h-4 transition-colors duration-200",
                          project.status === "Active" ? "text-green-600" : "text-blue-600"
                        ))}
                        <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                          {project.name}
                        </span>
                      </div>
                      <Badge
                        variant={project.status === "Active" ? "default" : "secondary"}
                        className={cn(
                          "text-xs px-3 py-1 font-medium",
                          project.status === "Active"
                            ? "bg-green-500/10 text-green-600 border-green-500/20"
                            : "bg-blue-500/10 text-blue-600 border-blue-500/20"
                        )}
                      >
                        {project.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* What I'm Learning */}
            <Card className="group hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm transform hover:scale-[1.005]">
              <CardHeader className="pb-6">
                <CardTitle className="flex items-center gap-3 text-xl font-bold">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                    <BookOpen className="w-5 h-5 text-primary" />
                  </div>
                  What I'm Learning
                </CardTitle>
                <CardDescription className="text-muted-foreground/80">
                  Technologies and concepts I'm actively studying
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentlyLearning.map((item) => (
                    <div key={item.name} className="flex items-center gap-4 p-4 rounded-xl hover:bg-muted/30 transition-colors duration-200 group/item border border-border/30">
                      {renderIcon(item.icon, "w-4 h-4 text-primary/70 animate-pulse")}
                      <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                        {item.name}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-6 pt-4 border-t border-border/50">
                  <p className="text-sm text-muted-foreground/70">
                    Continuously expanding my skillset to stay current with modern web development practices.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </Section>

        {/* Professional Journey Timeline */}
        <Section 
          title="My Journey"
          subtitle="Key milestones and achievements in my development journey."
        >
          <Card className="border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 transform hover:scale-[1.005]">
            <CardContent className="p-8">
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute top-8 left-8 right-8 h-[2px] bg-gradient-to-r from-primary/20 via-primary/60 to-primary/20"></div>

                {/* Timeline Items */}
                <div className="flex flex-col md:flex-row justify-between relative">
                  {journeyMilestones.map((milestone) => (
                    <motion.div
                      key={milestone.date}
                      className="flex flex-col items-center text-center group mb-8 md:mb-0 md:flex-1"
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      {/* Icon */}
                      <div className={cn(
                        "relative z-10 w-16 h-16 bg-background border-4 rounded-full flex items-center justify-center mb-4 transition-all duration-300 group-hover:scale-105 group-hover:shadow-soft-lg",
                        milestone.isPresent
                          ? "border-primary bg-primary/10 animate-pulse shadow-lg shadow-primary/20"
                          : "border-primary/20 group-hover:border-primary group-hover:bg-primary/10"
                      )}>
                        {renderIcon(milestone.icon, cn(
                          "w-6 h-6 transition-colors duration-300",
                          milestone.isPresent
                            ? "text-primary"
                            : "group-hover:text-primary"
                        ))}
                      </div>

                      {/* Content */}
                      <div className="space-y-2 max-w-[200px]">
                        <Badge
                          variant="outline"
                          className={cn(
                            "text-xs px-2 py-1",
                            milestone.isPresent && "bg-primary/10 text-primary border-primary/30 animate-pulse",
                            !milestone.isPresent && milestone.type === "education" && "bg-blue-500/10 text-blue-600 border-blue-500/20",
                            !milestone.isPresent && milestone.type === "career" && "bg-green-500/10 text-green-600 border-green-500/20",
                            !milestone.isPresent && milestone.type === "project" && "bg-purple-500/10 text-purple-600 border-purple-500/20",
                            !milestone.isPresent && milestone.type === "skill" && "bg-orange-500/10 text-orange-600 border-orange-500/20"
                          )}
                        >
                          {milestone.date}
                        </Badge>
                        <h3 className={cn(
                          "font-semibold text-sm transition-colors duration-300",
                          milestone.isPresent
                            ? "text-primary"
                            : "group-hover:text-primary"
                        )}>
                          {milestone.title}
                        </h3>
                        <p className="text-xs text-muted-foreground/70 leading-relaxed">
                          {milestone.description}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </Section>

        {/* Final Call to Action */}
        <Section animate={false}>
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 rounded-2xl"></div>
            <Card className="relative border-border/50 bg-background/80 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 transform hover:scale-[1.005]">
              <CardContent className="p-12 text-center">
                <div className="space-y-8">
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-slate-800 dark:text-slate-100 tracking-tight">
                      Have a project in mind?
                    </h2>
                    <p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto leading-relaxed mt-6">
                      I'm always open to discussing new projects and opportunities.
                      Let's create something amazing together.
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-6">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        onClick={() => navigate('/contact')}
                        size="lg"
                        className="group relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out px-8 py-6 text-base font-semibold border-0 rounded-xl overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                        <span className="relative z-10">Contact Me</span>
                        <ArrowRight className="relative z-10 ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 ease-in-out" />
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        onClick={() => navigate('/about')}
                        variant="outline"
                        size="lg"
                        className="group relative bg-white/90 hover:bg-white/95 border-2 border-gray-200/80 hover:border-blue-300/60 text-gray-700 hover:text-blue-700 px-8 py-6 text-base font-semibold transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-lg backdrop-blur-sm overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/50 to-blue-50/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                        <span className="relative z-10">Learn More About Me</span>
                        <ArrowRight className="relative z-10 ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out opacity-0 group-hover:opacity-100" />
                      </Button>
                    </motion.div>
                  </div>

                  <div className="pt-8 border-t border-border/50">
                    <p className="text-sm text-muted-foreground/70">
                      After graduating with a Computer Engineering degree in May 2024, I discovered my passion for frontend
                      development. I'm committed to continuous learning and building applications that provide exceptional user experiences.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Section>

        {/* Floating Contact Button */}
        <motion.div
          className="fixed bottom-8 right-8 z-50"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 2, duration: 0.5, type: "spring" }}
        >
          <motion.div
            animate={{
              boxShadow: [
                "0 0 0 0 rgba(59, 130, 246, 0.4)",
                "0 0 0 10px rgba(59, 130, 246, 0)",
                "0 0 0 0 rgba(59, 130, 246, 0)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="rounded-full"
          >
            <Button
              onClick={() => setIsChatOpen(true)}
              size="lg"
              className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 group border-0"
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Bot className="w-6 h-6" />
              </motion.div>
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Chat Assistant Modal */}
      <AnimatePresence>
        {isChatOpen && (
          <ChatAssistant onClose={() => setIsChatOpen(false)} />
        )}
      </AnimatePresence>
    </>
  )
}