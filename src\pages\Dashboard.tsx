import React, { useState } from "react"
import { ArrowRight, CheckSquare, Code, Folder, BookOpen, GraduationCap, Rocket, ShoppingCart, Sparkles, Bot } from "lucide-react"
import { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiJavascript, SiGit } from "react-icons/si"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { useNavigate } from "react-router-dom"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

import DeviceMockup from "@/components/DeviceMockup"
import ChatAssistant from "@/components/ChatAssistant"
import Section from "@/components/Section"
import InteractiveTerminal from "@/components/InteractiveTerminal"

export default function Dashboard() {
  const navigate = useNavigate()
  const [isChatOpen, setIsChatOpen] = useState(false)



  // Optimized animation variants with reduced delays
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4
      }
    }
  }

  const staggerContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  }

  const featuredProjects = [
    {
      id: 1,
      title: "E-commerce Platform",
      description: "A full-stack retail app built with React and Next.js",
      image: "/api/placeholder/600/400",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"],
      status: "Completed",
      highlight: "Full-stack development with modern architecture"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "Collaborative workspace with real-time updates",
      image: "/api/placeholder/600/400",
      technologies: ["React", "Prisma", "PostgreSQL", "Tailwind CSS"],
      status: "In Progress",
      highlight: "Real-time collaboration features"
    },
    {
      id: 3,
      title: "Portfolio Website",
      description: "Modern portfolio showcasing projects and skills",
      image: "/api/placeholder/600/400",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      status: "Completed",
      highlight: "Clean design with smooth interactions"
    }
  ]

  const coreSkills = [
    { name: "React", category: "Frontend", icon: SiReact },
    { name: "TypeScript", category: "Frontend", icon: SiTypescript },
    { name: "Next.js", category: "Frontend", icon: SiNextdotjs },
    { name: "Tailwind CSS", category: "Styling", icon: SiTailwindcss },
    { name: "JavaScript", category: "Frontend", icon: SiJavascript },
    { name: "Git", category: "Tools", icon: SiGit }
  ]

  // Helper function to render icons safely
  const renderIcon = (IconComponent: any, className: string) => {
    if (typeof IconComponent === 'function') {
      return React.createElement(IconComponent, { className })
    }
    return null
  }

  const currentlyLearning = [
    { name: "Advanced React Patterns", icon: Code },
    { name: "TypeScript Best Practices", icon: BookOpen },
    { name: "Frontend Performance", icon: Rocket },
    { name: "UI/UX Design Principles", icon: Sparkles }
  ]

  const workingOn = [
    { name: "Personal Portfolio Website", status: "Active", icon: Folder },
    { name: "E-commerce Platform", status: "Planning", icon: ShoppingCart },
    { name: "Task Management App", status: "Planning", icon: CheckSquare }
  ]

  return (
    <>
      <div className="relative container mx-auto px-6 md:px-8 py-12 md:py-16 max-w-6xl z-10">
      
        {/* Hero Section */}
        <motion.div
          className="mb-24 md:mb-32"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8 order-1 lg:order-1">
              <div className="space-y-6">
                <motion.div
                  className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.05 }}
                >
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  Available for new opportunities
                </motion.div>

                <motion.h1
                  className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-800 dark:text-slate-100 leading-tight tracking-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  I craft digital experiences that{' '}
                  <motion.span
                    className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    users love
                  </motion.span>
                </motion.h1>

                <motion.p
                  className="text-lg md:text-xl text-slate-600 dark:text-slate-300 leading-relaxed"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  Fresh Computer Engineering graduate turned frontend developer.
                  I build modern, responsive web applications with clean code and exceptional user experiences.
                </motion.p>

                <motion.div
                  className="bg-slate-900/10 dark:bg-slate-100/10 rounded-lg p-4 border border-slate-200/50 dark:border-slate-700/50"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                >
                  <p className="text-sm text-slate-600 dark:text-slate-400 font-mono">
                    <span className="text-primary">→</span> Try the interactive terminal on the right!
                  </p>
                  <p className="text-xs text-slate-500 dark:text-slate-500 font-mono mt-1">
                    Type <code className="bg-slate-200 dark:bg-slate-800 px-1 rounded">help</code> to get started
                  </p>
                </motion.div>
              </div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 pt-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.1 }}
              >
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate('/projects')}
                    size="lg"
                    className="group relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out px-8 py-4 text-base font-semibold border-0 rounded-xl overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                    <span className="relative z-10">View My Work</span>
                    <ArrowRight className="relative z-10 ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 ease-in-out" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate('/about')}
                    variant="outline"
                    size="lg"
                    className="group relative bg-white/90 hover:bg-white/95 border-2 border-gray-200/80 hover:border-blue-300/60 text-gray-700 hover:text-blue-700 px-8 py-4 text-base font-semibold transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-lg backdrop-blur-sm overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/50 to-blue-50/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                    <span className="relative z-10">About My Journey</span>
                    <ArrowRight className="relative z-10 ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out opacity-0 group-hover:opacity-100" />
                  </Button>
                </motion.div>
              </motion.div>
            </div>

            {/* Right Column - Interactive Terminal */}
            <div className="order-2 lg:order-2">
              <InteractiveTerminal />
            </div>
          </div>
        </motion.div>

        {/* Featured Projects */}
        <Section
          title="Featured Work"
          subtitle="A selection of my best projects showcasing modern web development practices and user-centered design."
        >
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainerVariants}
          >
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {featuredProjects.map((project) => (
                  <CarouselItem key={project.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                    <motion.div
                      variants={itemVariants}
                      whileHover={{ y: -2, scale: 1.005 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="h-full"
                    >
                  <Card className="group min-w-[350px] md:min-w-[400px] hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm hover:border-primary/20">
                  <div className="aspect-video bg-gradient-to-br from-primary/5 to-primary/10 rounded-t-lg overflow-hidden relative flex items-center justify-center p-6">
                    <DeviceMockup
                      type={project.id === 1 ? 'desktop' : project.id === 2 ? 'mobile' : 'tablet'}
                      className="max-h-full group-hover:scale-[1.02] transition-transform duration-300"
                    >
                      {project.id === 1 && (
                        <div className="p-4 space-y-3 bg-white">
                          {/* E-commerce header */}
                          <div className="flex items-center justify-between">
                            <div className="h-6 bg-blue-600 rounded px-2 flex items-center">
                              <ShoppingCart className="w-3 h-3 text-white mr-1" />
                              <span className="text-xs text-white font-medium">ShopHub</span>
                            </div>
                            <div className="flex gap-1">
                              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            </div>
                          </div>
                          {/* Product grid */}
                          <div className="grid grid-cols-3 gap-2">
                            <div className="bg-gray-50 rounded p-2 space-y-1">
                              <div className="h-8 bg-blue-200 rounded"></div>
                              <div className="h-2 bg-gray-200 rounded"></div>
                              <div className="h-2 bg-green-500 rounded w-1/2"></div>
                            </div>
                            <div className="bg-gray-50 rounded p-2 space-y-1">
                              <div className="h-8 bg-purple-200 rounded"></div>
                              <div className="h-2 bg-gray-200 rounded"></div>
                              <div className="h-2 bg-green-500 rounded w-1/2"></div>
                            </div>
                            <div className="bg-gray-50 rounded p-2 space-y-1">
                              <div className="h-8 bg-orange-200 rounded"></div>
                              <div className="h-2 bg-gray-200 rounded"></div>
                              <div className="h-2 bg-green-500 rounded w-1/2"></div>
                            </div>
                          </div>
                          {/* Cart summary */}
                          <div className="flex items-center justify-between bg-blue-50 rounded p-2">
                            <span className="text-xs text-gray-600">3 items in cart</span>
                            <div className="h-3 bg-blue-600 rounded px-2 text-xs text-white flex items-center">$99</div>
                          </div>
                        </div>
                      )}
                      {project.id === 2 && (
                        <div className="p-3 space-y-2 bg-white">
                          {/* Task app header */}
                          <div className="flex items-center justify-between bg-slate-100 rounded p-2">
                            <div className="flex items-center gap-1">
                              <CheckSquare className="w-3 h-3 text-blue-600" />
                              <span className="text-xs font-medium">TaskFlow</span>
                            </div>
                            <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                              <span className="text-xs text-white">3</span>
                            </div>
                          </div>
                          {/* Task list */}
                          <div className="space-y-1">
                            <div className="bg-green-50 border-l-2 border-green-500 rounded flex items-center px-2 py-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <div className="flex-1">
                                <div className="h-2 bg-green-200 rounded mb-1"></div>
                                <div className="h-1 bg-green-100 rounded w-2/3"></div>
                              </div>
                              <CheckSquare className="w-3 h-3 text-green-600" />
                            </div>
                            <div className="bg-blue-50 border-l-2 border-blue-500 rounded flex items-center px-2 py-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              <div className="flex-1">
                                <div className="h-2 bg-blue-200 rounded mb-1"></div>
                                <div className="h-1 bg-blue-100 rounded w-3/4"></div>
                              </div>
                              <div className="w-3 h-3 border border-blue-300 rounded"></div>
                            </div>
                            <div className="bg-orange-50 border-l-2 border-orange-500 rounded flex items-center px-2 py-1">
                              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                              <div className="flex-1">
                                <div className="h-2 bg-orange-200 rounded mb-1"></div>
                                <div className="h-1 bg-orange-100 rounded w-1/2"></div>
                              </div>
                              <div className="w-3 h-3 border border-orange-300 rounded"></div>
                            </div>
                          </div>
                        </div>
                      )}
                      {project.id === 3 && (
                        <div className="p-4 space-y-3 bg-white">
                          {/* Portfolio header */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded"></div>
                              <span className="text-xs font-bold text-gray-800">Chris.dev</span>
                            </div>
                            <div className="flex gap-1">
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                            </div>
                          </div>
                          {/* Hero section mockup */}
                          <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded p-3 space-y-2">
                            <div className="h-3 bg-gray-800 rounded w-3/4"></div>
                            <div className="h-2 bg-gray-400 rounded w-1/2"></div>
                            <div className="flex gap-1 mt-2">
                              <div className="h-2 bg-blue-500 rounded px-2"></div>
                              <div className="h-2 bg-purple-500 rounded px-2"></div>
                            </div>
                          </div>
                          {/* Projects grid */}
                          <div className="grid grid-cols-2 gap-2">
                            <div className="bg-gray-50 rounded p-2 space-y-1">
                              <div className="h-4 bg-blue-200 rounded"></div>
                              <div className="h-1 bg-gray-200 rounded"></div>
                              <div className="h-1 bg-gray-200 rounded w-2/3"></div>
                            </div>
                            <div className="bg-gray-50 rounded p-2 space-y-1">
                              <div className="h-4 bg-green-200 rounded"></div>
                              <div className="h-1 bg-gray-200 rounded"></div>
                              <div className="h-1 bg-gray-200 rounded w-2/3"></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </DeviceMockup>
                    <Badge
                      className={cn(
                        "absolute top-4 right-4 font-medium",
                        project.status === "Completed" || project.status === "Active"
                          ? "bg-green-500/10 text-green-600 border-green-500/20"
                          : project.status === "In Progress" || project.status === "Planning"
                          ? "bg-blue-500/10 text-blue-600 border-blue-500/20"
                          : "bg-gray-500/10 text-gray-600 border-gray-500/20"
                      )}
                    >
                      {project.status}
                    </Badge>
                  </div>

                  <CardHeader className="pb-6 space-y-4">
                    <CardTitle className="group-hover:text-primary transition-colors duration-300 text-xl font-bold">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-300 leading-relaxed">
                      {project.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <div className="flex flex-wrap gap-3">
                      {project.technologies.slice(0, 3).map((tech) => (
                        <Badge key={tech} variant="outline" className="text-xs font-medium px-3 py-1">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge variant="outline" className="text-xs font-medium px-3 py-1">
                          +{project.technologies.length - 3} more
                        </Badge>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <Button
                        onClick={() => navigate('/projects')}
                        variant="ghost"
                        className="flex-1 group/btn bg-slate-50 hover:bg-blue-50 hover:text-blue-700 text-slate-600 transition-all duration-300 ease-in-out border border-slate-200 hover:border-blue-200 rounded-lg hover:shadow-sm"
                      >
                        Live Demo
                        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-300 ease-in-out" />
                      </Button>
                      <Button
                        onClick={() => navigate('/projects')}
                        variant="outline"
                        className="flex-1 group/btn bg-white hover:bg-slate-50 hover:text-slate-700 text-slate-600 transition-all duration-300 ease-in-out border border-slate-200 hover:border-slate-300 rounded-lg hover:shadow-sm"
                      >
                        View Code
                        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-300 ease-in-out" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                    </motion.div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="hidden md:flex" />
              <CarouselNext className="hidden md:flex" />
            </Carousel>
          </motion.div>

          <div className="text-center mt-12">
            <Button
              onClick={() => navigate('/projects')}
              variant="outline"
              className="group bg-white/80 hover:bg-white/95 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-soft-lg backdrop-blur-sm transform hover:scale-[1.02]"
            >
              See all projects
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out" />
            </Button>
          </div>
        </Section>

        {/* Skills Snapshot */}
        <Section 
          title="Core Technologies"
          subtitle="The tools and technologies I use to bring ideas to life."
        >
          <Card className="border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300">
            <CardContent className="p-8">
              <motion.div
                className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={staggerContainerVariants}
              >
                {coreSkills.map((skill) => (
                  <motion.div
                    key={skill.name}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -1 }}
                    className="group flex flex-col items-center space-y-3 p-4 rounded-xl hover:bg-primary/5 transition-all duration-300"
                    aria-label={`${skill.name} - ${skill.category} technology`}
                  >
                    <motion.div
                      className="text-3xl"
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      transition={{ duration: 0.2 }}
                    >
                      {renderIcon(skill.icon, "w-8 h-8")}
                    </motion.div>
                    <div className="text-center">
                      <p className="font-semibold text-sm group-hover:text-primary transition-colors duration-300">
                        {skill.name}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {skill.category}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>

              <div className="text-center mt-8 pt-6 border-t border-border/50">
                <Button
                  onClick={() => navigate('/skills')}
                  variant="outline"
                  className="group bg-white/80 hover:bg-white/95 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 transition-all duration-300 rounded-xl shadow-sm hover:shadow-soft-lg backdrop-blur-sm transform hover:scale-[1.02]"
                >
                  Explore My Full Skillset
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </Section>

        {/* Current Focus */}
        <Section 
          title="Current Focus"
          subtitle="What I'm building and learning right now to stay at the forefront of web development."
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* What I'm Building */}
            <Card className="group hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm transform hover:scale-[1.005]">
              <CardHeader className="pb-6">
                <CardTitle className="flex items-center gap-3 text-xl font-bold">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                    <Folder className="w-5 h-5 text-primary" />
                  </div>
                  What I'm Building
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Current projects and ongoing development
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {workingOn.map((project) => (
                    <div key={project.name} className="flex items-center justify-between p-4 rounded-xl hover:bg-muted/30 transition-colors duration-200 group/item border border-border/30">
                      <div className="flex items-center gap-3">
                        {renderIcon(project.icon, cn(
                          "w-4 h-4 transition-colors duration-200",
                          project.status === "Active" ? "text-green-600" : "text-blue-600"
                        ))}
                        <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                          {project.name}
                        </span>
                      </div>
                      <Badge
                        variant={project.status === "Active" ? "default" : "secondary"}
                        className={cn(
                          "text-xs px-3 py-1 font-medium",
                          project.status === "Active"
                            ? "bg-green-500/10 text-green-600 border-green-500/20"
                            : "bg-blue-500/10 text-blue-600 border-blue-500/20"
                        )}
                      >
                        {project.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* What I'm Learning */}
            <Card className="group hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-background/50 backdrop-blur-sm transform hover:scale-[1.005]">
              <CardHeader className="pb-6">
                <CardTitle className="flex items-center gap-3 text-xl font-bold">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                    <BookOpen className="w-5 h-5 text-primary" />
                  </div>
                  What I'm Learning
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Technologies and concepts I'm actively studying
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentlyLearning.map((item) => (
                    <div key={item.name} className="flex items-center gap-4 p-4 rounded-xl hover:bg-muted/30 transition-colors duration-200 group/item border border-border/30">
                      {renderIcon(item.icon, "w-4 h-4 text-primary/70 animate-pulse")}
                      <span className="font-medium text-muted-foreground group-hover/item:text-foreground transition-colors duration-200">
                        {item.name}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-6 pt-4 border-t border-border/50">
                  <p className="text-sm text-muted-foreground">
                    Continuously expanding my skillset to stay current with modern web development practices.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </Section>

        {/* Journey Summary */}
        <Section
          title="My Journey"
          subtitle="From Computer Engineering graduate to passionate Frontend Developer."
        >
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={itemVariants}
          >
            <Card className="border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="space-y-6">
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <GraduationCap className="w-5 h-5 text-blue-600" />
                      <span className="text-sm font-medium text-muted-foreground">May 2025</span>
                    </div>
                    <div className="w-8 h-[2px] bg-gradient-to-r from-blue-600 to-primary"></div>
                    <div className="flex items-center space-x-2">
                      <Rocket className="w-5 h-5 text-primary" />
                      <span className="text-sm font-medium text-primary">Present</span>
                    </div>
                  </div>

                  <p className="text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                    After graduating with a Computer Engineering degree in May 2025, I discovered my passion for frontend
                    development. I'm committed to continuous learning and building applications that provide exceptional user experiences.
                  </p>

                  <Button
                    onClick={() => navigate('/about')}
                    variant="outline"
                    className="group bg-white/80 hover:bg-white/95 border-gray-200 hover:border-primary/30 text-gray-700 hover:text-primary transition-all duration-300"
                  >
                    See my full journey
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </Section>

        {/* Final Call to Action */}
        <Section animate={false}>
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 rounded-2xl"></div>
            <Card className="relative border-border/50 bg-background/80 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 transform hover:scale-[1.005]">
              <CardContent className="p-12 text-center">
                <div className="space-y-8">
                  <div className="space-y-4">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-slate-800 dark:text-slate-100 tracking-tight">
                      Have a project in mind?
                    </h2>
                    <p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto leading-relaxed mt-6">
                      I'm always open to discussing new projects and opportunities.
                      Let's create something amazing together.
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-6">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        onClick={() => navigate('/contact')}
                        size="lg"
                        className="group relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out px-8 py-6 text-base font-semibold border-0 rounded-xl overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                        <span className="relative z-10">Contact Me</span>
                        <ArrowRight className="relative z-10 ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 ease-in-out" />
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        onClick={() => navigate('/about')}
                        variant="outline"
                        size="lg"
                        className="group relative bg-white/90 hover:bg-white/95 border-2 border-gray-200/80 hover:border-blue-300/60 text-gray-700 hover:text-blue-700 px-8 py-6 text-base font-semibold transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-lg backdrop-blur-sm overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/50 to-blue-50/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                        <span className="relative z-10">Learn More About Me</span>
                        <ArrowRight className="relative z-10 ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-in-out opacity-0 group-hover:opacity-100" />
                      </Button>
                    </motion.div>
                  </div>

                  <div className="pt-8 border-t border-border/50">
                    <p className="text-sm text-muted-foreground">
                      After graduating with a Computer Engineering degree in May 2025, I discovered my passion for frontend
                      development. I'm committed to continuous learning and building applications that provide exceptional user experiences.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Section>

        {/* Floating Contact Button */}
        <motion.div
          className="fixed bottom-8 right-8 z-50"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 2, duration: 0.5, type: "spring" }}
        >
          <motion.div
            animate={{
              boxShadow: [
                "0 0 0 0 rgba(59, 130, 246, 0.4)",
                "0 0 0 10px rgba(59, 130, 246, 0)",
                "0 0 0 0 rgba(59, 130, 246, 0)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className="rounded-full"
          >
            <Button
              onClick={() => setIsChatOpen(true)}
              size="lg"
              className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 group border-0"
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Bot className="w-6 h-6" />
              </motion.div>
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Chat Assistant Modal */}
      <AnimatePresence>
        {isChatOpen && (
          <ChatAssistant onClose={() => setIsChatOpen(false)} />
        )}
      </AnimatePresence>
    </>
  )
}